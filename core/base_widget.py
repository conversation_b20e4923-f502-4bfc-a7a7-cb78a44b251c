# -*- coding: utf-8 -*-

from __future__ import annotations

import time
import logging
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

from PyQt6.QtCore import QObject, QTimer, pyqtSignal

__all__ = ["ZenohWidget"]

logger = logging.getLogger("ZenohWidget")

class ZenohWidget(QObject):
    """Base class for GUI widgets consuming Zenoh data."""

    # ---------------------------------------------------- Qt signals
    data_updated = pyqtSignal(str, object)        # source_name, value
    data_freshness_changed = pyqtSignal(str, bool)  # source_name, is_fresh

    # ---------------------------------------------------- construction
    def __init__(
        self,
        core: Any,
        *args,
        data_timeout: float = 5.0,
        fixed_namespace: Optional[str] = None,
        **kwargs,
    ) -> None:
        super().__init__(*args, **kwargs)
        self.core = core
        self.zenoh = getattr(core, "zenoh_client", None)

        self.data_sources: Dict[str, Dict[str, Any]] = {}
        self._handles: List[Any] = []            # SubscriptionHandle list
        self.current_namespace: Optional[str] = None

        # "Sticky" namespace support ------------------------------
        # If *fixed_namespace* is provided, the widget will always
        # subscribe to that namespace and ignore global vehicle
        # changes coming from *core.vehicle_changed*.
        self.fixed_namespace: Optional[str] = fixed_namespace

        # Freshness tracking --------------------------------------
        self.data_timeout = data_timeout
        self.last_update_time: Dict[str, float] = {}
        self.data_is_fresh: Dict[str, bool] = {}
        self.freshness_timer = QTimer(self)
        self.freshness_timer.timeout.connect(self._check_data_freshness)

        # React to namespace changes ---------------------------------
        # Widgets with fixed namespace do not need to listen for
        # global vehicle changes.
        if self.fixed_namespace is None and hasattr(self.core, "vehicle_changed"):
            self.core.vehicle_changed.connect(self.on_namespace_changed)

        # Each widget may implement specialised handlers -------------
        self.data_updated.connect(self._dispatch_update)

    # ---------------------------------------------------- namespace helpers
    def _get_current_namespace(self) -> Optional[str]:
        """Return the namespace this widget should use.

        If *fixed_namespace* was specified at construction/prepare time,
        that value is returned unconditionally, effectively making the
        widget "sticky" to a particular vehicle. Otherwise the namespace
        is resolved from the *core* object as before.
        """
        if self.fixed_namespace is not None:
            return self.fixed_namespace

        in_rc = getattr(self.core, "in_rc_vehid", None)
        watched = getattr(self.core, "watched_vehid", None)
        ns = in_rc or watched
        if ns and hasattr(self.core, "veh_active_states"):
            if not self.core.veh_active_states.get(ns, False):
                ns = None
        return ns

    def on_namespace_changed(self):
        new_ns = self._get_current_namespace()
        if new_ns == self.current_namespace:
            return
        self.unsubscribe()
        self.current_namespace = new_ns
        if self.current_namespace and self.zenoh:
            self.subscribe()
        # mark all sources stale so UI can grey‑out
        for src in self.data_sources:
            self._set_data_freshness(src, False)

    # ---------------------------------------------------- runtime API
    def prepare(self, *,
                data_sources: Optional[Dict[str, Dict[str, Any]]] = None,
                data_timeout: Optional[float] = None,
                fixed_namespace: Optional[str] = None,
                **_: Any):
        if data_sources is not None:
            self.data_sources = data_sources
            for src in data_sources:
                self.data_is_fresh[src] = False
        if data_timeout is not None:
            self.data_timeout = data_timeout

        # Allow supplying fixed namespace via *prepare()* as well
        # (overrides constructor value).
        if fixed_namespace is not None:
            self.fixed_namespace = fixed_namespace
            # Stop listening to global namespace changes if we switch
            # to sticky behaviour at runtime.
            if hasattr(self.core, "vehicle_changed"):
                try:
                    self.core.vehicle_changed.disconnect(self.on_namespace_changed)
                except Exception:
                    pass

    def start(self):
        self.current_namespace = self._get_current_namespace()
        if self.current_namespace and self.zenoh:
            self.subscribe()
        self.freshness_timer.start(1000)

    # ---------------------------------------------------- subscription logic
    def subscribe(self):
        if not self.zenoh or not self.current_namespace:
            logger.warning(f"Cannot subscribe: zenoh={bool(self.zenoh)}, ns={self.current_namespace}")
            return
        self.unsubscribe()  # safety first

        grouped: Dict[Tuple[str, str], List[Tuple[str, str, float]]] = defaultdict(list)
        for src_name, cfg in self.data_sources.items():
            try:
                grouped[(cfg["topic"], cfg["msg_type"])] .append(
                    (src_name, cfg["field"], cfg.get("multiplier", 1.0))
                )
            except KeyError as e:
                logger.error(f"Incomplete cfg for {src_name}: missing {e}")

        for (topic, msg_type), fields in grouped.items():
            logger.debug(f"Subscribing to {topic} ({msg_type}) in namespace {self.current_namespace}")

            def _handler(_key: str, msg: object, *, _fields=fields):
                logger.debug(f"Received message on {_key}: {msg}")
                for s_name, field, k in _fields:
                    try:
                        # Get field value
                        val = getattr(msg, field)
                        # Only apply multiplier to numeric types, not strings
                        if not isinstance(val, (str, bool)):
                            val = val * k
                        logger.debug(f"  - Extracted field '{field}' for source '{s_name}': {val}")
                        self.data_updated.emit(s_name, val)
                        self.last_update_time[s_name] = time.time()
                        if not self.data_is_fresh.get(s_name):
                            self._set_data_freshness(s_name, True)
                    except AttributeError as e:
                        logger.error(f"Cannot extract field '{field}' from message: {e}")

            handle = self.zenoh.subscribe(topic, msg_type, self.current_namespace, callback=_handler)
            self._handles.append(handle)

    def unsubscribe(self):
        if not self.zenoh:
            return
        for h in self._handles:
            try:
                self.zenoh.unsubscribe(h)
            except Exception as e:
                logger.error(f"Error during unsubscribe: {e}")
        self._handles.clear()

    # ---------------------------------------------------- publishing helper
    def publish(self, topic: str, msg_type: str, namespace: Optional[str] = None, **kwargs):
        """Publish a message to Zenoh.
        
        Args:
            topic: Topic name
            msg_type: Message type (e.g. 'drill_msgs/msg/Permission')
            namespace: Namespace to use (defaults to current namespace)
            **kwargs: Additional message fields
            
        Returns:
            True if message was published, False otherwise
        """
        if not self.zenoh:
            logger.warning("Cannot publish: no Zenoh client")
            return False
            
        ns = namespace or self.current_namespace
        if not ns:
            logger.warning(f"Cannot publish to topic {topic}: no namespace")
            return False
            
        try:
            self.zenoh.publish(
                key_expr=topic,
                msg_type=msg_type,
                namespace=ns,
                **kwargs
            )
            return True
        except Exception as e:
            logger.error(f"Error publishing to {topic} in namespace {ns}: {e}")
            return False

    # ---------------------------------------------------- data freshness
    def _set_data_freshness(self, source: str, fresh: bool):
        if self.data_is_fresh.get(source) != fresh:
            logger.debug(f"Source '{source}' in ns '{self.current_namespace}' freshness changed: {fresh}")
            self.data_is_fresh[source] = fresh
            self.data_freshness_changed.emit(source, fresh)

    def _check_data_freshness(self):
        now = time.time()
        for src, t in list(self.last_update_time.items()):
            if now - t > self.data_timeout and self.data_is_fresh.get(src, False):
                logger.debug(f"Source {src} in ns '{self.current_namespace}' is stale: {now - t:.1f}s > {self.data_timeout}s")
                self._set_data_freshness(src, False)

    # ---------------------------------------------------- dispatch helpers
    def _dispatch_update(self, source_name: str, value: Any):
        logger.debug(f"Dispatching update for '{source_name}' in ns '{self.current_namespace}': {value}")
        
        # Apply any transforms defined in data_sources
        if source_name in self.data_sources and "transform" in self.data_sources[source_name]:
            transform = self.data_sources[source_name]["transform"]
            transform_type = transform.get("type")
            
            if transform_type == "equals":
                # Check if the value equals a specified value
                value = value == transform.get("value")
            elif transform_type == "not_equals":
                # Check if the value does not equal a specified value
                value = value != transform.get("value")
            elif transform_type == "in":
                # Check if the value is in a list of values
                value = value in transform.get("values", [])
            elif transform_type == "not_in":
                # Check if the value is not in a list of values
                value = value not in transform.get("values", [])
            
            logger.debug(f"Applied transform '{transform_type}' to '{source_name}', result: {value}")
            
        # fallback generic handler hook for concrete widgets
        if hasattr(self, "handle_update"):
            self.handle_update(source_name, value)
        spc = getattr(self, f"handle_{source_name}", None)
        if callable(spc):
            spc(value)

    # ---------------------------------------------------- convenience
    def is_data_fresh(self, source: Optional[str] = None) -> bool:
        if source is not None:
            return self.data_is_fresh.get(source, False)
        return bool(self.data_is_fresh) and all(self.data_is_fresh.values())

    def cleanup(self):
        self.freshness_timer.stop()
        self.unsubscribe()
        if hasattr(self.core, "vehicle_changed"):
            try:
                self.core.vehicle_changed.disconnect(self.on_namespace_changed)
            except Exception:
                pass
