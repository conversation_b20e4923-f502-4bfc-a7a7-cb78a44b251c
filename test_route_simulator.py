#!/usr/bin/env python3
"""
ROS2 Route Simulator для тестирования умной логики routes в Map Widget.

Этот скрипт имитирует:
1. Топик driver_status с прогрессом по маршруту
2. Сервис get_current_routes для получения полных маршрутов
3. Топик position с движением по маршруту
4. Различные сценарии (смена action_id, прогресс по точкам)

Использует zenoh для публикации данных в формате, совместимом с нашей системой.
"""

import json
import time
import math
import threading
from typing import List, Dict, Any
import sys
import os

# Добавляем путь к zenoh_client для импорта
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

try:
    import zenoh
    print("Zenoh imported successfully")
except ImportError:
    print("Error: zenoh not found. Install with: pip install eclipse-zenoh")
    sys.exit(1)

class ZenohRouteSimulator:
    def __init__(self, namespace: str = "local_sim"):
        """Инициализация симулятора."""
        self.namespace = namespace
        self.session = None

        # Состояние симуляции
        self.current_action_id = 1
        self.current_segment_id = 0
        self.current_point_id = 0
        self.current_position = [0.0, 0.0, 0.0]  # x, y, yaw

        # Создаем тестовые маршруты
        self.test_routes = self.create_test_routes()

        # Флаг для остановки
        self.running = False

        print(f"Route Simulator initialized for namespace '{namespace}'")
        print(f"Created {len(self.test_routes)} test route segments")

    def create_test_routes(self) -> List[Dict[str, Any]]:
        """Создаем тестовые маршруты для демонстрации."""
        routes = []

        # Сегмент 1: Прямая линия на восток
        segment1 = {
            "points": []
        }
        for i in range(10):
            point = {
                "x": float(i * 2),  # Каждые 2 метра
                "y": 0.0,
                "z": 0.0
            }
            segment1["points"].append(point)
        routes.append(segment1)

        # Сегмент 2: Поворот на север
        segment2 = {
            "points": []
        }
        for i in range(8):
            point = {
                "x": 18.0,  # Конец первого сегмента
                "y": float(i * 2),
                "z": 0.0
            }
            segment2["points"].append(point)
        routes.append(segment2)

        # Сегмент 3: Диагональ на северо-запад
        segment3 = {
            "points": []
        }
        for i in range(6):
            point = {
                "x": 18.0 - float(i * 1.5),
                "y": 14.0 + float(i * 1.5),
                "z": 0.0
            }
            segment3["points"].append(point)
        routes.append(segment3)

        return routes

    def start(self):
        """Запуск симулятора."""
        try:
            # Открываем zenoh сессию
            config = zenoh.Config()
            self.session = zenoh.open(config)
            print("Zenoh session opened")

            # Регистрируем сервис
            service_key = f"{self.namespace}/get_current_routes"
            self.session.declare_queryable(service_key, self.handle_service_request)
            print(f"Service registered: {service_key}")

            self.running = True

            # Запускаем потоки для публикации данных
            position_thread = threading.Thread(target=self.publish_position_loop)
            status_thread = threading.Thread(target=self.publish_status_loop)
            progress_thread = threading.Thread(target=self.update_progress_loop)

            position_thread.daemon = True
            status_thread.daemon = True
            progress_thread.daemon = True

            position_thread.start()
            status_thread.start()
            progress_thread.start()

            print("Simulator started! Press Ctrl+C to stop.")

            # Основной цикл
            while self.running:
                time.sleep(0.1)

        except KeyboardInterrupt:
            print("\nStopping simulator...")
        finally:
            self.stop()

    def stop(self):
        """Остановка симулятора."""
        self.running = False
        if self.session:
            self.session.close()
            print("Zenoh session closed")

    def publish_position_loop(self):
        """Поток для публикации позиции (10 Hz)."""
        while self.running:
            self.publish_position()
            time.sleep(0.1)

    def publish_status_loop(self):
        """Поток для публикации статуса (1 Hz)."""
        while self.running:
            self.publish_driver_status()
            time.sleep(1.0)

    def update_progress_loop(self):
        """Поток для обновления прогресса (каждые 3 секунды)."""
        while self.running:
            self.update_progress()
            time.sleep(3.0)

    def publish_position(self):
        """Публикуем текущую позицию робота."""
        # Получаем текущую целевую точку
        target_point = self.get_current_target_point()
        if target_point:
            # Плавно движемся к целевой точке
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]

            # Простая логика движения
            speed = 0.1  # м/с
            if abs(dx) > 0.1 or abs(dy) > 0.1:
                distance = math.sqrt(dx*dx + dy*dy)
                self.current_position[0] += (dx / distance) * speed
                self.current_position[1] += (dy / distance) * speed
                self.current_position[2] = math.atan2(dy, dx)  # yaw

        # Создаем сообщение позиции
        position_msg = {
            "header": {
                "stamp": {"sec": int(time.time()), "nanosec": 0},
                "frame_id": "map"
            },
            "x": self.current_position[0],
            "y": self.current_position[1],
            "z": 0.0,
            "yaw": self.current_position[2],
            "is_reliable": True
        }

        # Публикуем
        topic_key = f"{self.namespace}/position"
        self.session.put(topic_key, json.dumps(position_msg))

    def publish_driver_status(self):
        """Публикуем статус драйвера."""
        status_msg = {
            "header": {
                "stamp": {"sec": int(time.time()), "nanosec": 0},
                "frame_id": "map"
            },
            "cur_action_id": self.current_action_id,
            "last_action_id": self.current_action_id - 1 if self.current_action_id > 1 else 0,
            "cur_segment_id": self.current_segment_id,
            "cur_point_id": self.current_point_id,
            "status": "executing"
        }

        # Публикуем
        topic_key = f"{self.namespace}/driver_status"
        self.session.put(topic_key, json.dumps(status_msg))

        # Логирование для отладки
        print(f'Status: action_id={status_msg["cur_action_id"]}, '
              f'segment={status_msg["cur_segment_id"]}, point={status_msg["cur_point_id"]}')

    def update_progress(self):
        """Обновляем прогресс движения по маршруту."""
        # Проверяем достигли ли мы текущей точки
        target_point = self.get_current_target_point()
        if target_point:
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)

            # Если близко к точке, переходим к следующей
            if distance < 0.5:  # 50 см
                self.advance_to_next_point()

    def get_current_target_point(self) -> Dict[str, float]:
        """Получаем текущую целевую точку."""
        if (self.current_segment_id < len(self.test_routes) and
            self.current_point_id < len(self.test_routes[self.current_segment_id]["points"])):
            return self.test_routes[self.current_segment_id]["points"][self.current_point_id]
        return None

    def advance_to_next_point(self):
        """Переходим к следующей точке маршрута."""
        current_segment = self.test_routes[self.current_segment_id]

        # Переходим к следующей точке в текущем сегменте
        if self.current_point_id < len(current_segment["points"]) - 1:
            self.current_point_id += 1
            print(f'Advanced to point {self.current_point_id}')

        # Переходим к следующему сегменту
        elif self.current_segment_id < len(self.test_routes) - 1:
            self.current_segment_id += 1
            self.current_point_id = 0
            print(f'Advanced to segment {self.current_segment_id}')

        # Завершили все сегменты - начинаем новый action
        else:
            self.current_action_id += 1
            self.current_segment_id = 0
            self.current_point_id = 0
            print(f'Started new action {self.current_action_id}')

            # Сбрасываем позицию для нового маршрута
            self.current_position = [0.0, 0.0, 0.0]

    def handle_service_request(self, query):
        """Обработчик запросов сервиса get_current_routes."""
        print(f'Service called: get_current_routes for action_id={self.current_action_id}')

        response = {
            "header": {
                "stamp": {"sec": int(time.time()), "nanosec": 0},
                "frame_id": "map"
            },
            "action_id": self.current_action_id,
            "path_segments": self.test_routes.copy(),
            "success": True,
            "message": f"Returned {len(self.test_routes)} path segments"
        }

        print(f'Service response: {len(response["path_segments"])} segments')
        query.reply(json.dumps(response))


def main():
    """Главная функция."""
    import sys

    # Получаем namespace из аргументов командной строки
    namespace = "local_sim"
    if len(sys.argv) > 1:
        namespace = sys.argv[1]

    # Создаем и запускаем симулятор
    simulator = ZenohRouteSimulator(namespace)
    simulator.start()


if __name__ == '__main__':
    main()
