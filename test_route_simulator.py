#!/usr/bin/env python3
"""
ROS2 Route Simulator для тестирования умной логики routes в Map Widget.

Этот скрипт имитирует:
1. Топик driver_status с прогрессом по маршруту
2. Сервис get_current_routes для получения полных маршрутов
3. Топик position с движением по маршруту
4. Различные сценарии (смена action_id, прогресс по точкам)
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
import math
import time
from typing import List, Tuple

# Импорты сообщений (нужно будет создать эти типы)
from std_msgs.msg import Header
from builtin_interfaces.msg import Time
from geometry_msgs.msg import Point

# Временные классы для имитации drill_msgs (пока нет реальных)
class Position:
    def __init__(self):
        self.header = Header()
        self.x = 0.0
        self.y = 0.0
        self.z = 0.0
        self.yaw = 0.0
        self.is_reliable = True

class DriveStatus:
    def __init__(self):
        self.header = Header()
        self.cur_action_id = 0
        self.last_action_id = 0
        self.cur_segment_id = 0
        self.cur_point_id = 0
        self.status = "idle"

class Path:
    def __init__(self):
        self.points = []  # List of Point

class GetCurrentRoutesRequest:
    pass

class GetCurrentRoutesResponse:
    def __init__(self):
        self.header = Header()
        self.action_id = 0
        self.path_segments = []  # List of Path
        self.success = True
        self.message = ""

class RouteSimulator(Node):
    def __init__(self):
        super().__init__('route_simulator')

        # QoS профили
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            durability=DurabilityPolicy.TRANSIENT_LOCAL,
            depth=10
        )

        # Publishers
        self.position_pub = self.create_publisher(Position, 'position', qos_profile)
        self.driver_status_pub = self.create_publisher(DriveStatus, 'driver_status', qos_profile)

        # Service
        # self.routes_service = self.create_service(
        #     GetCurrentRoutes, 'get_current_routes', self.get_current_routes_callback
        # )

        # Таймеры
        self.position_timer = self.create_timer(0.1, self.publish_position)  # 10 Hz
        self.status_timer = self.create_timer(1.0, self.publish_driver_status)  # 1 Hz
        self.progress_timer = self.create_timer(3.0, self.update_progress)  # Прогресс каждые 3 сек

        # Состояние симуляции
        self.current_action_id = 1
        self.current_segment_id = 0
        self.current_point_id = 0
        self.current_position = [0.0, 0.0, 0.0]  # x, y, yaw

        # Создаем тестовые маршруты
        self.test_routes = self.create_test_routes()

        self.get_logger().info('Route Simulator started!')
        self.get_logger().info(f'Created {len(self.test_routes)} test route segments')

    def create_test_routes(self) -> List[Path]:
        """Создаем тестовые маршруты для демонстрации."""
        routes = []

        # Сегмент 1: Прямая линия на восток
        segment1 = Path()
        for i in range(10):
            point = Point()
            point.x = float(i * 2)  # Каждые 2 метра
            point.y = 0.0
            point.z = 0.0
            segment1.points.append(point)
        routes.append(segment1)

        # Сегмент 2: Поворот на север
        segment2 = Path()
        for i in range(8):
            point = Point()
            point.x = 18.0  # Конец первого сегмента
            point.y = float(i * 2)
            point.z = 0.0
            segment2.points.append(point)
        routes.append(segment2)

        # Сегмент 3: Диагональ на северо-запад
        segment3 = Path()
        for i in range(6):
            point = Point()
            point.x = 18.0 - float(i * 1.5)
            point.y = 14.0 + float(i * 1.5)
            point.z = 0.0
            segment3.points.append(point)
        routes.append(segment3)

        return routes

    def publish_position(self):
        """Публикуем текущую позицию робота."""
        msg = Position()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.header.frame_id = "map"

        # Получаем текущую целевую точку
        target_point = self.get_current_target_point()
        if target_point:
            # Плавно движемся к целевой точке
            dx = target_point.x - self.current_position[0]
            dy = target_point.y - self.current_position[1]

            # Простая логика движения
            speed = 0.1  # м/с
            if abs(dx) > 0.1 or abs(dy) > 0.1:
                distance = math.sqrt(dx*dx + dy*dy)
                self.current_position[0] += (dx / distance) * speed
                self.current_position[1] += (dy / distance) * speed
                self.current_position[2] = math.atan2(dy, dx)  # yaw

        msg.x = self.current_position[0]
        msg.y = self.current_position[1]
        msg.z = self.current_position[2]
        msg.yaw = self.current_position[2]
        msg.is_reliable = True

        self.position_pub.publish(msg)

    def publish_driver_status(self):
        """Публикуем статус драйвера."""
        msg = DriveStatus()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.header.frame_id = "map"

        msg.cur_action_id = self.current_action_id
        msg.last_action_id = self.current_action_id - 1 if self.current_action_id > 1 else 0
        msg.cur_segment_id = self.current_segment_id
        msg.cur_point_id = self.current_point_id
        msg.status = "executing"

        self.driver_status_pub.publish(msg)

        # Логирование для отладки
        self.get_logger().info(
            f'Status: action_id={msg.cur_action_id}, '
            f'segment={msg.cur_segment_id}, point={msg.cur_point_id}'
        )

    def update_progress(self):
        """Обновляем прогресс движения по маршруту."""
        # Проверяем достигли ли мы текущей точки
        target_point = self.get_current_target_point()
        if target_point:
            dx = target_point.x - self.current_position[0]
            dy = target_point.y - self.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)

            # Если близко к точке, переходим к следующей
            if distance < 0.5:  # 50 см
                self.advance_to_next_point()

    def get_current_target_point(self) -> Point:
        """Получаем текущую целевую точку."""
        if (self.current_segment_id < len(self.test_routes) and
            self.current_point_id < len(self.test_routes[self.current_segment_id].points)):
            return self.test_routes[self.current_segment_id].points[self.current_point_id]
        return None

    def advance_to_next_point(self):
        """Переходим к следующей точке маршрута."""
        current_segment = self.test_routes[self.current_segment_id]

        # Переходим к следующей точке в текущем сегменте
        if self.current_point_id < len(current_segment.points) - 1:
            self.current_point_id += 1
            self.get_logger().info(f'Advanced to point {self.current_point_id}')

        # Переходим к следующему сегменту
        elif self.current_segment_id < len(self.test_routes) - 1:
            self.current_segment_id += 1
            self.current_point_id = 0
            self.get_logger().info(f'Advanced to segment {self.current_segment_id}')

        # Завершили все сегменты - начинаем новый action
        else:
            self.current_action_id += 1
            self.current_segment_id = 0
            self.current_point_id = 0
            self.get_logger().info(f'Started new action {self.current_action_id}')

            # Сбрасываем позицию для нового маршрута
            self.current_position = [0.0, 0.0, 0.0]

    def get_current_routes_callback(self, request, response):
        """Callback для сервиса получения маршрутов."""
        self.get_logger().info(f'Service called: get_current_routes for action_id={self.current_action_id}')

        response.header.stamp = self.get_clock().now().to_msg()
        response.header.frame_id = "map"
        response.action_id = self.current_action_id
        response.path_segments = self.test_routes.copy()
        response.success = True
        response.message = f"Returned {len(self.test_routes)} path segments"

        self.get_logger().info(f'Service response: {len(response.path_segments)} segments')
        return response


def main(args=None):
    """Главная функция."""
    rclpy.init(args=args)

    # Создаем ноду
    simulator = RouteSimulator()

    try:
        # Запускаем симулятор
        rclpy.spin(simulator)
    except KeyboardInterrupt:
        simulator.get_logger().info('Simulator stopped by user')
    finally:
        simulator.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
